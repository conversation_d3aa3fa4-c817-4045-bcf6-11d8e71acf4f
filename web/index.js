// @ts-check
import { join } from "path";
import { readFileSync } from "fs";
import express from "express";
import serveStatic from "serve-static";
import dotenv from 'dotenv';
import { createRequire } from "module";
const require = createRequire(import.meta.url);
import logger from "./services/logger.js";
import integrations from "./services/airbyte/integrations.js";
import automation, { KLAVIYO_RFM_TAGGING } from "./services/automation/index.js";
import {getActiveSubscription, getActiveSubscriptionsFromShopify, updateSubscription, saveSubscription, upsertSubscription} from "./services/subscription.js";

dotenv.config();
import {SESSION_COOKIE_NAME, STATE_COOKIE_NAME} from "@shopify/shopify-api";
import shopify from "./shopify.js";
import shop, { slackAlert } from './services/shop.js';
import TokenService from './services/token.js';
import user from './services/user.js';
import facebook from './services/facebook/index.js';
import ltv from './services/ltv/index.js';
import tracker from './services/tracker.js';
import {cache} from './services/redis.js';
const { parse } = require('json2csv');
const jwt = require('jsonwebtoken');
import admin from 'firebase-admin';
import exporter from './services/exporter/index.js';
import GDPRWebhookHandlers from "./gdpr.js";
import klaviyo from "./services/klaviyo/index.js";
import { SLACK_CLIENT_ID, getSlackData, generateAuthUrl, disconnectSlack, saveSlackAccessToken } from "./services/slack/index.js";
import settings from "./services/settings/index.js";
import errors from "./services/errors.js";
import unleash from "./services/unleash.js";
import axios from "axios";
import reporter from "./services/reporter/index.js";
const XLSX = require('xlsx');

import googleIntegration from "./services/airbyte/googleIntegration.js";
import { SOURCE_FB, SOURCE_GOOGLE_ADS, SOURCE_GOOGLE_ANALYTICS, SOURCE_KLAVIYO, V1_PLATFORM_OSS, VALID_SOURCE_TYPES} from "./services/airbyte/constants.js";
import { 
  shopifyBillingConfig,
  getAvailablePlans,
  getActivePlanDetails,
  getRecommendedPlanDetails,
  OLD_PRICING_PLANS,
  NEW_PRICING_PLANS,
  evaluateCoupon
} from "./services/subscription/plans.js";
import {DBT_PROD_DATASET_OSS} from './services/dbt/index.js';
import { Client } from "@langchain/langgraph-sdk";
import UserInvitation from "./services/user/invitation/index.js";
import WorkspaceUser from "./services/workspace/workspace_user.js";
import Identification from "./services/auth/identification/index.js";
import User from "./services/auth/user/index.js";
import { WORKSPACE_ROLE_OWNER, WORKSPACE_ROLE_ADMIN } from "./services/workspace/workspace_user.js";
import Workspace from "./services/workspace/index.js";
import { getMetadataForSearchThreads} from "./services/prashna/index.js";

// if (process.env.NODE_ENV !== 'production') {
//     var serviceAccount = require("./shopify-301218-firebase-adminsdk-w8pxp-42fc438efb.json");
//     admin.initializeApp({
//         credential: admin.credential.cert(serviceAccount)
//     });
// } else {
    admin.initializeApp();
// }

const PORT = parseInt(process.env.BACKEND_PORT || process.env.PORT, 10);

const STATIC_PATH =
  process.env.NODE_ENV === "production"
    ? `${process.cwd()}/frontend/dist`
    : `${process.cwd()}/frontend/`;

const app = express();

// Set up Shopify authentication and webhook handling
// app.get(shopify.config.auth.path, shopify.auth.begin());
app.get(shopify.config.auth.path, async function (req, res, next) {
  if (!!req.query.shop && req.query.shop !== 'undefined') {
    // check if shop is already installed - if no, then track a try install event


    // regex match shop should end with .myshopify.com
    const regex = new RegExp(/^[a-z\d_.-]+\.myshopify\.com$/);
    if (regex.test(req.query.shop)) {
      const shopObj = await shop.getShopByOrigin(req.query.shop)
      if (!shopObj || !shopObj.shop_id) {
        var ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress 
        tracker.track(req.query.shop, 'Shop Try Installation', {
          ip: ip
        })
      }
    }

    return await shopify.auth.begin()(req, res, next)
  } else {
    logger.warn("Invalid shop name - no shop try installation", {
      shop : req.query.shop,
      headers : req.headers
    });
    res.redirect('/sign-in');
  }
});

// userAuthMiddleware()  is used where only user session is required - otherwise 401
// userAuthMiddleware(true) is used where user session is optional - but then shopify session is required - otherwise 401
let userAuthMiddleware = function (optional = false) {
  return async function (req, res, next) {
    // idToken is JWT token that comes from the client app
    let idToken = req.headers["AuthIdToken"] ?? req.headers["authidtoken"] ?? req.headers["Authidtoken"];

    // custom auth token is JWT token that are used for all non-user invocations (ex. from cloud scheduler)
    const customAuthToken = req.headers["CustomAuthToken"] ?? req.headers["customauthtoken"];

    if (!idToken && !customAuthToken) {
      if (optional) {
        return next();
      } else {
        return res.status(401).send('Unauthorized');
      }
    }

    try {
      if (idToken) {
        const decodedToken = await admin.auth().verifyIdToken(idToken);
        if (decodedToken) {
          res.locals.user = decodedToken;
        }
      } else if (customAuthToken) {
        const decodedToken = jwt.verify(customAuthToken, process.env.CUSTOM_JWT_SECRET, {'algorithm':'HS256'});
        if (decodedToken) {
          res.locals.user = decodedToken;
        }
      }
    } catch (err) {
      logger.error(`userAuth error - ${err}`, {headers : req.headers})
      if (optional) {
        return await next();
      } else {
        return res.status(401).send('Unauthorized');
      }
    }

    await next();
  }
}

app.get(
  shopify.config.auth.callbackPath,
  shopify.auth.callback(),
  shop.afterAuth,
  shopify.redirectToShopifyOrAppRoot()
);
app.post(
  shopify.config.webhooks.path,
  shopify.processWebhooks({ webhookHandlers: GDPRWebhookHandlers })
);

// function to read a particular cookie from express request object
function getCookie(req, name) {
  try {
    var value = "; " + req.headers.cookie;
    var parts = value.split("; " + name + "=");
    if (parts.length == 2) {
      return parts.pop().split(";").shift();
    } else {
      return ""
    }
  } catch (err) {
    logger.error("getCookie error - ", err)
    return ""
  }
}

// authMiddleware - used in most of the APIs to authenticate the request
// atleast one should be present - user session or shopify session
let authMiddleware = [userAuthMiddleware(true), async function (req, res, next) {
  let authedUser = res.locals.user ?? {}
  let shopifySessionCookie = getCookie(req, SESSION_COOKIE_NAME)
  if (!authedUser || !authedUser.uid) {
    // if no user session found, shopify session is mandatory
    return await shopify.validateAuthenticatedSession()(req, res, next)
  } else if (shopifySessionCookie !== "") {
    // both user and shopify session is valid
    return await shopify.validateAuthenticatedSession()(req, res, next)
  } else {
    // only user session is valid - this user may be a random user as well
    // but we'll allow the request to go through resolve* middlewares for authorization
    return await next();
  }
}];


// resolveMiddlware - used in most of the APIs to resolve the request data - like user data and shop data
// atleast selected data shop from user session or a shopify session should be present
let resolveSelectedShop = [user.resolveUser, shop.resolveShop, async function (req, res, next) {
  // if user data not saved or shop data not saved - resolveSelectedShop will throw 401
  if (!req.body.resolved_login_id) {
    return res.status(401).send('Unauthorized');
  }

  // if no shop data found, resolveSelectedShop will throw 401
  let isShopDataPresent = !!req.body.shop && !!req.body.shop.shop_id;
  if (!isShopDataPresent) {
    return res.status(401).send('Unauthorized');
  }

  return await next()
}]

// for /api/login - there can be a case when user is logged in but there's no store mapped to the user
// In this case - we can still allow the user to login and then show the onboarding screen
let resolveUserShop = [user.resolveUser, shop.resolveShop, async function (req, res, next) {

  if (!req.body.resolved_login_id) {
    return res.status(401).send('Unauthorized');
  }

  return await next()
}]

// All endpoints /api/* will require user or shopify session
// except /api/user/* & some other will require user session only

app.use(express.json());

app.post('/api/user/onboarding', userAuthMiddleware(), async (req, res) => {

  let response = {status : false};
  if (!!res.locals.user && !!res.locals.user.uid) {
    response = await user.saveOnboardingDetails(res.locals.user.uid, req.body)
  }

  res.json(response)
})

app.post('/api/user/verify-email', userAuthMiddleware(), async (req, res) => {
  try {
    const authedUser = res.locals.user ?? {}; // assuming the user is attached to the request by the authMiddleware
    if (!authedUser.email) {
      res.status(500).json({ error: 'Error sending verification email.' });
      return;
    }

    if (authedUser.email_verified) {
      res.status(200).json({ success: true, alreadyVerified: true });
      return;
    }

    let cacheKey = `verification_link_${authedUser.email}`
    let cachedLink = await cache.get(cacheKey)
    if (cachedLink) {
      res.json(await user.sendVerificationEmail(authedUser.email, cachedLink));
      return;
    }

    let link = await admin.auth().generateEmailVerificationLink(authedUser.email, {
      url: 'https://app.datadrew.io',
    });

    if (link) {
      await cache.set(cacheKey, link, 86400 * 2) // 2 days
    }

    res.json(await user.sendVerificationEmail(authedUser.email, link));
  } catch (error) {
    logger.error('Error sending verification email:', error);
    if (error.message.includes('TOO_MANY_ATTEMPTS_TRY_LATER')) {
      res.status(429).json({ error: 'Too many attempts. Please try again later.' });
    } else {
      res.status(500).json({ error: 'Error sending verification email.' });
    }
  }
});

app.post('/api/link-user-shop', authMiddleware, resolveUserShop, async (req, res) => {
  const {user_data, shop_data, shop_to_link} = req.body;
  if (!user_data || !user_data.email || !shop_data || !shop_data.shop_id || shop_data.myshopify_domain != shop_to_link) {
    shop.slackAlert("Error in api/link-user-shop : missing data")
    res.status(400).send("Bad Request")
    return;
  }

  let resp = await user.linkAuthedUserShop(req.body)
  if (resp.status) {
    // remove SESSION_COOKIE_NAME & STATE_COOKIE_NAME cookie from client
    res.clearCookie(SESSION_COOKIE_NAME); // clear session cookie
    res.clearCookie(`${SESSION_COOKIE_NAME}.sig`); // clear session cookie signature
    res.clearCookie(STATE_COOKIE_NAME);
  } else {
    shop.slackAlert("Error in api/link-user-shop : " + JSON.stringify({
      email : user_data.email,
      shop_id : shop_data.shop_id
    }))
  }

  res.json(resp);
});

app.post('/api/send-invitation', authMiddleware, resolveUserShop, async (req, res) => {
  const {name, email, role, workspace_id, shop_ids} = req.body;
  if (!email || !role || !workspace_id || !shop_ids) {
    shop.slackAlert("Error in api/send-invitation : missing data")
    res.status(400).send("Bad Request")
    return;
  }

  // Note: this API will only be invoked in the user flow (not in the shop flow)
  let user_invitation = new UserInvitation(res.locals.user)
    .setName(name)
    .setEmail(email)
    .setRole(role)
    .setWorkspaceId(workspace_id)
    .setShopIds(shop_ids);

  let resp = await user_invitation.createAndSendInvite();
  res.json(resp)
})

app.post('/api/update-invitation', authMiddleware, resolveUserShop, async (req, res) => {
  const {invitation_id, status} = req.body;
  if (!invitation_id || !status) {
    shop.slackAlert("Error in api/update-invitation : missing data")
    res.status(400).send("Bad Request")
    return;
  }

  // Note: this API will only be invoked in the user flow (not in the shop flow)
  let user_invitation = new UserInvitation(res.locals.user)
    .setInvitationId(invitation_id);

  let resp = await user_invitation.updateInvitation(status);
  res.json(resp)
})

app.post('/api/workspace/details', authMiddleware, resolveUserShop, async (req, res) => {
  console.log("workspace/details req.body: ", req.body.workspace_id);
  const {user_data, user_shops, workspace_id} = req.body;
  if (!workspace_id) {
    shop.slackAlert("Error in api/workspace/details : missing data")
    res.status(400).send("Bad Request")
    return;
  }

  let workspace_obj = new Workspace();
  let resp = await workspace_obj.getWorkspaceDetailsForUser(req.body.workspace_id, user_data.uid);
  res.json(resp)
})

const LANGRAPH_ASSISTANT_ID = "prashna_agent";

const createLanggraphClient = () => {
  return new Client({
    apiUrl: process.env.LANGGRAPH_API_URL,
    apiKey: process.env.LANGGRAPH_API_KEY
  });
};

// Endpoint to create a new thread
app.post('/api/agent/thread/create', authMiddleware, resolveSelectedShop, async (req, res) => {
  try {
    const client = createLanggraphClient();
    const response = await client.threads.create({
      metadata: {
        shop_id: req.body.shop.shop_id,
        is_admin: req.body.is_admin,
        is_user_flow: !!req.body.authed_user,
        user_email: req.body.user_data?.email ?? ""
      }
    });
    res.json(response);
  } catch (error) {
    logger.error('Error creating thread:', error);
    res.status(500).json({ error: 'Failed to create thread' });
  }
});

// Endpoint to get thread state
app.post('/api/agent/thread/state', authMiddleware, resolveSelectedShop, async (req, res) => {
  const { threadId } = req.body;
  try {
    const client = createLanggraphClient();
    const state = await client.threads.getState(threadId);
    res.json(state);
  } catch (error) {
    logger.error('Error getting thread state:', error);
    res.status(500).json({ error: 'Failed to get thread state' });
  }
});

// Endpoint to cancel a run
app.post('/api/agent/thread/cancel', authMiddleware, resolveSelectedShop, async (req, res) => {
  const { threadId, runId } = req.body;
  try {
    const client = createLanggraphClient();
    const result = await client.runs.cancel(threadId, runId, false, "interrupt");
    res.json(result);
  } catch (error) {
    logger.error('Error cancelling run:', error);
    res.status(500).json({ error: 'Failed to cancel run' });
  }
});

// Endpoint to get threads
app.post('/api/agent/threads', authMiddleware, resolveSelectedShop, async (req, res) => {
  const { limit, status } = req.body;
  try {
    const client = createLanggraphClient();
    const threads = await client.threads.search({
      limit: limit,
      status: status,
      metadata: getMetadataForSearchThreads(
        req.body.shop.shop_id,
        req.body.is_admin,
        !!req.body.authed_user,
        req.body.user_data?.email ?? ""
      )
    });
    res.json(threads);
  } catch (error) {
    logger.error('Error getting threads:', error);
    res.status(500).json({ error: 'Failed to get threads' });
  }
});

app.post('/api/agent/plotly', authMiddleware, resolveSelectedShop, async (req, res) => {
  const { plotly_json_path } = req.body;
  if (!plotly_json_path) {
    return res.status(400).json({ error: 'Invalid input' });
  }

  const GCS_BUCKET_NAME = "prashna-plots";

  try {
    // fetch the json from google cloud storage using the path
    const url =  `https://storage.googleapis.com/${GCS_BUCKET_NAME}/${plotly_json_path}`
    const response = await axios.get(url);
    res.json(response.data);
  } catch (error) {
    logger.error('Error getting plotly:', error);
    res.status(500).json({ error: 'Failed to read json' });
  }
});

// Endpoint to send a message
app.post('/api/agent/thread/send', authMiddleware, resolveSelectedShop, async (req, res) => {
  const { threadId } = req.body;
  const { messages } = req.body;
  const shop_id = req.body.shop.shop_id;

  if (!messages || messages.length === 0) {
    return res.status(400).json({ error: 'Invalid input' });
  }

  let useMigrationDataset = await shop.shouldUseMigrationDataset(req.body.shop.shop_id);
  let shopify_region = useMigrationDataset ? "eu" : "us";

  const intgs = await integrations.getIntegrations(shop_id, "", false, false)

  const googleAdsIntgs = (intgs[SOURCE_GOOGLE_ADS] ?? []).reduce((acc, intg) => {
    acc.integration_ids.push(intg.auto_id + '')
    return acc;
  }, {source: SOURCE_GOOGLE_ADS, integration_ids: []})

  const facebookIntgs = (intgs[SOURCE_FB] ?? []).reduce((acc, intg) => {
    acc.integration_ids.push(intg.auto_id + '')
    return acc;
  }, {source: SOURCE_FB, integration_ids: []})

  const googleAnalyticsIntgs = (intgs[SOURCE_GOOGLE_ANALYTICS] ?? []).reduce((acc, intg) => {
    acc.integration_ids.push(intg.auto_id + '')
    return acc;
  }, {source: SOURCE_GOOGLE_ANALYTICS, integration_ids: []})

  const integration_sources = [googleAdsIntgs, facebookIntgs, googleAnalyticsIntgs]
  const debug_mode = req.body.is_admin ?? false;

  try {
    const client = createLanggraphClient();
    const input = {
      user_query: messages[messages.length - 1].content
    };

    const stream = client.runs.stream(
      threadId,
      LANGRAPH_ASSISTANT_ID,
      {
        input: input,
        streamMode: "custom",
        config: { configurable: { shop_id, shopify_region, integration_sources, debug_mode } }
      },
    );

    // Set headers for server-sent events
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');

    let mergedCustomStreamOutput = {}
    // Manually iterate over the async generator
    for await (const chunk of stream) {
      // Extract event type and data
      const eventType = chunk.event || 'message'; // Default to 'message' if no event type is provided
      const eventData = chunk.data || chunk;

      if (eventType == "custom") {
        for (const key in eventData) {
          if (eventData[key]) {
            mergedCustomStreamOutput[key] = eventData[key];
          }
        }
        // Write event type and data to the response
        res.write(`event: ${eventType}\n`);
        res.write(`data: ${JSON.stringify(mergedCustomStreamOutput)}\n\n`);
      } else {
        // Write event type and data to the response as it is
        res.write(`event: ${eventType}\n`);
        res.write(`data: ${JSON.stringify(eventData)}\n\n`);
      }
    }
    res.end();

  } catch (error) {
    logger.error('Error sending message:', error);
    res.status(500).json({ error: 'Failed to send message' });
  }
});

app.post('/api/unlink-user-shop', authMiddleware, resolveUserShop, async (req, res) => {

  let resp = await user.unlinkUserShop(req.body);
  // a successful unlinking will remove any existing shopify session to prevent relinking of same store
  if (resp.status) {
    // remove SESSION_COOKIE_NAME & STATE_COOKIE_NAME cookie from client
    res.clearCookie(SESSION_COOKIE_NAME); // clear session cookie
    res.clearCookie(`${SESSION_COOKIE_NAME}.sig`); // clear session cookie signature
    res.clearCookie(STATE_COOKIE_NAME);
  }

  res.json(resp);
});

app.post('/api/basket-analysis' , authMiddleware, resolveSelectedShop, async (req, res) => {
  const response = await ltv.cartAnalysis(req.body)
  res.json(response)
})

app.post('/api/repurchase', authMiddleware, resolveSelectedShop, async (req, res) => {
  const response = await ltv.repurchaseRateBreakdown(req.body)
  res.json(response)
})

app.post('/api/fbauthover', authMiddleware, resolveSelectedShop, async (req, res) => {
  var resp = await facebook.afterAuth(req.body)
  res.json(resp)
})

app.post('/api/filter-options', authMiddleware, resolveSelectedShop, async (req, res) => {
  var resp = await ltv.getFilterOptions(req.body)
  res.json(resp)
})

app.post('/api/report/data', authMiddleware, resolveSelectedShop, async (req, res) => {
  try {
    const { type, filters, shop } = req.body;

    // Validate input
    if (!type || !filters) {
      return res.json({error : errors.client.invalidInput})
    }

    const response = await reporter.build(shop.shop_id, type, filters);
    res.json(response);
  } catch (error) {
    logger.error('Error fetching metrics:', error);
    res.json({error : errors.client.generic})
  }
});

app.get('/api/data/export', authMiddleware, resolveSelectedShop, async (req, res) => {

  if (!req.query.type || !req.query.parameters) {
    res.json({error : errors.client.invalidInput})
    return
  }

  try {
      const {error, data, fields} = await exporter.getJSONData({ ...req.body, ...req.query });
      if (error) {
        logger.error('/api/data/export: Data export error', {
          error: error,
          shop_id: req.body.shop.shop_id,
          query: req.query
        });
        return res.json({error : errors.client.generic})
      }

      let format = req.query.format ?? 'csv'
      switch (format) {
        case 'csv':
          const csv = parse(data, {fields});
          res.setHeader('Content-disposition', `attachment; filename=data.csv`);
          res.setHeader('Content-Type', 'text/csv');
          res.status(200);
          res.attachment('data.csv').send(csv)
          break;
        case 'xlsx':
          const ws = XLSX.utils.json_to_sheet(data);
          const wb = XLSX.utils.book_new();
          XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
          const buf = XLSX.write(wb, { bookType: 'xlsx', type: 'buffer' });
          res.setHeader('Content-disposition', `attachment; filename=data.xlsx`);
          res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
          res.status(200);
          res.attachment('data.xlsx').send(buf)
          break;
        case 'json':
          res.json(data)
          break;
        default:
          res.json({error : errors.client.invalidInput})
          break;
      }
  } catch (err) {
      logger.error('/api/data/export: Data export error', {
        error: err.message,
      });
      res.json({error : errors.client.generic})
  }
})

app.post('/api/settings', authMiddleware, resolveSelectedShop, async (req, res) => {
  let response = await settings.getShopSettings(req.body.shop.shop_id);
  res.json(response);
});


app.post('/api/settings/update', authMiddleware, resolveSelectedShop, async (req, res) => {

  if (!req.body.settings || (!req.body.settings.shopInfo && !req.body.settings.shopSettings)) {
    res.json({error : errors.client.invalidInput})
    return
  }

  if (!!req.body.settings.shopInfo) {
    let shopObj = await shop.updatePartial(req.body.shop.shop_id, req.body.settings.shopInfo)
    if (!shopObj || !shopObj.shop_id) {
      res.json({error : errors.client.generic})
      return
    }
  }

  // update seettings one by one for each setting_key
  if (!!req.body.settings.shopSettings) {
    for (var key in req.body.settings.shopSettings) {
      let setting = req.body.settings.shopSettings[key]
      let done = await settings.addOrUpdateSettings(req.body.shop.shop_id, key, setting)
      if (!done) {
        res.json({error : errors.client.generic})
        return
      }
    }
  }

  res.json({status : true})
})

app.post('/api/workspace/member/update', authMiddleware, resolveUserShop, async (req, res) => {
  const { workspace_id, user_id, data } = req.body;
  const response = await WorkspaceUser.updateWorkspaceMember(workspace_id, user_id, data);
  res.json(response)
})

app.post('/api/workspace/update', authMiddleware, resolveUserShop, async (req, res) => {
  const { workspace_id, name } = req.body;
  if (!workspace_id || !name) {
    shop.slackAlert("Error in api/workspace/update : missing data")
    res.status(400).send("Bad Request")
    return;
  }

  let workspace_obj = new Workspace();
  let resp = await workspace_obj.updateWorkspaceName(workspace_id, name);
  res.json(resp)
})


app.post('/api/cprofile', authMiddleware, resolveSelectedShop, async (req, res) => {
  const response = await ltv.profile(req.body)
  res.json(response)
})

app.post('/api/onboarding', authMiddleware, resolveSelectedShop, async (req, res) => {
  const response = await shop.saveOnboardingDetails(req.body)
  res.json(response)
})

app.post('/api/cohorts', authMiddleware, resolveSelectedShop, async (req, res) => {
  const response = await ltv.cohorts(req.body);
  res.json(response)
})

app.post("/api/integrations/disconnect", authMiddleware, resolveSelectedShop, async (req, res) => {

  let request_id = req.body.request_id;
  if (!request_id) {
    res.json({error : errors.client.invalidInput})
    return
  }

  let done = await integrations.handleDisconnect(request_id);
  if (!done) {
    res.json({error : errors.client.generic})
    return
  }

  let isDisabled = await automation.handleIntegrationDisconnect(request_id);
  if (!isDisabled) {
    res.json({error : errors.client.generic})
    return
  }

  res.json({success : true})
});

app.post("/api/job-status", authMiddleware, resolveSelectedShop, async (req, res) => {
  const request_id = req.body.request_id;
  const jobStatus = await integrations.getJobStatus(request_id);
  res.json(jobStatus);
});

app.post("/api/integrations", authMiddleware, resolveSelectedShop, async (req, res) => {
  let source_type = req.body.source_type ?? "";
  let dataIntegrations = await integrations.getIntegrations(req?.body?.shop?.shop_id, source_type, true, true)
  res.json(dataIntegrations);
});

app.post('/api/integrations/connect', authMiddleware, resolveSelectedShop, async (req, res) => {
  const source_type = req.body.source_type;
  if (!source_type || VALID_SOURCE_TYPES.indexOf(source_type) == -1) {
    logger.error("Invalid source_type", {source_type});
    return res.json({error: errors.client.invalidInput});
  }

  try {
    let result = await integrations.handleConnect(req.body.shop.shop_id, source_type) 
    return res.json(result);
  } catch (error) {
    logger.error(error.message, {source_type});
    return res.json({error: errors.client.generic});
  }
});

app.post('/api/integration/slack', authMiddleware, resolveSelectedShop, async (req, res) => {

  let slackData = await getSlackData(req.body.shop.shop_id);
  // we have to add shop_id param to the slack auth url so that we can identify the shop
  // and save the access token in the DB
  let shopAuthurl = await generateAuthUrl(req.body.shop.shop_id)
  if (!slackData || !slackData.shop_id) {
    return res.json({ 
      slack_connected : false,
      channel : "",
      slack_auth_url : shopAuthurl
     });
  }


  let response = {
    slack_connected : !!slackData.active_flag,
    channel : slackData.channel_name ?? "",
    slack_auth_url : shopAuthurl
  }

  res.json(response);
})


app.get("/api/slack/oauth/callback", authMiddleware, resolveSelectedShop, async (req, res) => {
  const { code, sid } = req.query;

  if (!code || !sid) {
      return res.json({error: 'Missing code parameter'});
  }

  if (sid != req.body.shop.shop_id) {
    logger.info('/api/slack/oauth/callback: authed shop_id different', {
      shop_id: req.body.shop.shop_id,
      sid: sid
    });
  }

  const redirect_uri = `${process.env.HOST}/api/slack/oauth/callback?sid=${sid}`;
  try {
    const response = await axios.post('https://slack.com/api/oauth.v2.access', null, {
      params: {
          client_id: SLACK_CLIENT_ID,
          client_secret: process.env.SLACK_CLIENT_SECRET,
          code: code,
          redirect_uri: redirect_uri,
      },
    });

    if (!response.data.ok) {
      logger.error('/api/slack/oauth/callback: Error fetching access token:', {
        shop_id: sid,
        error: response?.data?.error ?? ""
      });
      return res.redirect(`/settings?err=1&opt=scheduled-reports`);
    }

    const saved = await saveSlackAccessToken(response.data, sid);
    if (!saved) {
      logger.error('/api/slack/oauth/callback: Error saving access token:', {
        shop_id: sid
      });
      return res.redirect(`/settings?err=1&opt=scheduled-reports`);
    }

    // settings will be updated to enable the weekly report
    // A job will be created to send the weekly report
    const settingsAdded = await settings.addOrUpdateSettings(
      sid,
      settings.SLACK_WEEKLY_REPORT,
      {active_flag : 1}
    );

    if (!settingsAdded) {
      logger.error('/api/slack/oauth/callback: Error saving settings:', {
        shop_id: sid
      });
      return res.redirect(`/settings?err=1&opt=scheduled-reports`);
    }

    return res.redirect(`/settings&opt=scheduled-reports`);
  } catch (error) {
      logger.error('/api/slack/oauth/callback: Error during OAuth process:', error);
      return res.redirect(`/settings?err=1&opt=scheduled-reports`);
  }
});

app.post('/api/integration/slack/disconnect', authMiddleware, resolveSelectedShop, async(req,res) =>{
  const done = await disconnectSlack(req.body.shop.shop_id);
  if (!done) {
    return res.json({error: errors.client.generic});
  }
  return res.json({success: 'Slack disconnected successfully'});
})

app.post('/api/integration/klaviyo/connect', authMiddleware, resolveSelectedShop, async (req, res) => {
  const klaviyo_api_key = req.body.klaviyo_api_key;

  if (!klaviyo_api_key) {
    res.json({ error: errors.client.invalidInput });
    return;
  }

  try {
      const isValidApiKey = await klaviyo.testConnection(klaviyo_api_key);
      if (!isValidApiKey) {
        res.json({error: "Connection test failed. Please check the API key and try again."});
        return;
      }

      await integrations.setupKlaviyoConnection(req.body.shop.shop_id, klaviyo_api_key, V1_PLATFORM_OSS);
      res.json({success: true})
    } catch (error) {
      logger.error('Error in /api/integration/klaviyo/connect', error);
      res.json({error: errors.client.generic})
    }
});

app.post('/api/automation/klaviyo/submit', authMiddleware, resolveSelectedShop, async (req, res) => {
  const client_shop_id = req.body.shop.shop_id;
  const { segment, request_id, period } = req.body;

  let jobSource = {
    shop_id : client_shop_id,
    rfm_segment: segment,
    period
  }

  let jobDestination = {
    integration_request_id: request_id
  }

  let done = await automation.submit(client_shop_id, KLAVIYO_RFM_TAGGING, jobSource, jobDestination)

  if (done.error || !done.job_id) {
    res.json({error: errors.client.generic})
    return
  }

  res.json({success: true, job_id: done.job_id})
});

app.post('/api/automation/klaviyo/sync-weekly/run', authMiddleware, resolveSelectedShop, async (req, res) => {
  const client_shop_id = req.body.shop.shop_id;
  const { job_id } = req.body;

  if (!job_id) {
    res.json({error: errors.client.invalidInput});
    return;
  }

  let job = await automation.getJob(job_id)
  if (!job || !job.job_id || job.shop_id != client_shop_id) {
    res.json({error: errors.client.invalidInput});
    return;
  }

  await shop.pushAsyncTask({action: "automation", job_id : job_id})
  res.json({success: true});
});

app.post('/api/automation/klaviyo/sync-weekly', authMiddleware, resolveSelectedShop, async (req, res) => {
  const client_shop_id = req.body.shop.shop_id;
  const { request_id, enabled } = req.body;

  if (!enabled) {
    const job_id = req.body.job_id;
    if (!job_id) {
      logger.error("Error in disabling /api/automation/klaviyo/sync-weekly, job_id not present.")
      res.json({error: errors.client.invalidInput});
      return;
    }

    const partial_configuration = {
      active_flag: 0
    }

    const done = await automation.updatePartial(job_id, partial_configuration);
    if (!done) {
      res.json({error: errors.client.generic});
      return;
    }

    res.json({success: true});
  } else {
    const done = await automation.makeKlaviyoWeeklySyncJob(client_shop_id, request_id);
    if (!done) {
      logger.error("Error in /api/automation/klaviyo/sync-weekly", {client_shop_id, request_id})
      res.json({error: errors.client.generic});
      return;
    }
    res.json({success: true});
  }
});

app.post('/api/automation/jobs', authMiddleware, resolveSelectedShop, async (req, res) => {
  const shop_id = req.body.shop.shop_id;
  if (!shop_id) {
    res.json({ error: "Invalid shop_id" });
    return;
  }

  if (!req.body.job_type) {
    res.json({ error: "Invalid job_type" });
    return;
  }

  try {
    let jobs = await automation.getJobs(shop_id, req.body.job_type);
    jobs = jobs.map(job => {
      job.source = JSON.parse(job.source);
      job.destination = JSON.parse(job.destination);
      return job;
    });
    res.send(jobs);
  } catch (error) {
    res.json({ error: errors.client.generic });
  }
});


let getAlerts = async ({resolved_login_id, user_data, subscription}) => {

  if (!!user_data && Object.keys(user_data).length > 0) {
      return {has_alerts:false}
  }

  let isDisabled = !unleash.isEnabled('subscription-alert', {
    myshopify_domain : resolved_login_id
  });

  if (isDisabled) {
    return {has_alerts: false}
  }

  let alerts = {}
  let blacklist_records = await shop.getBlacklistRecords()

  if (!!blacklist_records && Object.keys(blacklist_records).length > 0) {
    if (blacklist_records.review && blacklist_records.review.indexOf(resolved_login_id) === -1) {
      alerts.review = false // has not worked, changing to false
    } 
    if (blacklist_records.subscribe && blacklist_records.subscribe.indexOf(resolved_login_id) === -1) {
      alerts.subscribe = true
      if (subscription && subscription.subscription_id) {
        alerts.subscribe = false
      }
    }
  }

  return alerts
}

app.post('/api/alerts', authMiddleware, resolveSelectedShop, async (req, res) => {
  res.json({
      alerts: await getAlerts(req.body),
      facebook : {showConnector : false}
  })
});

app.post('/api/plans', authMiddleware, resolveSelectedShop, async (req, res) => {
  let coupon = req.body.coupon ?? "";
  let { shop_id } = req.body.shop;

  if (!shop_id) {
    res.json({error : errors.client.unauthorized})
    return
  }

  if (!!coupon) {
    coupon = coupon.trim().toUpperCase();
  }

  try {
    const plans = await getAvailablePlans(shop_id, coupon);
    res.json(plans)
  } catch (err) {
    logger.error('Error fetching plans', {shop_id, error: err.message});
    res.json({error : errors.client.generic})
  }
});

app.post('/api/plan/paywall', authMiddleware, resolveSelectedShop, async (req, res) => {
  const { shop_id } = req.body.shop;
  if (!shop_id) {
    res.json({error : errors.client.unauthorized})
    return
  }

  let feature_access = req.body.feature_access ?? "";
  let paywall = { 
    currentPlan: await getActivePlanDetails(shop_id),
    recommendedPlan : await getRecommendedPlanDetails(shop_id, feature_access)
  }

  return res.json(paywall);
});

app.post('/api/plan/cancel', authMiddleware, resolveSelectedShop, async (req, res) => {

  let subs = await getActiveSubscription(req.body.shop.shop_id)
  if (!subs || !subs.subscription_id) {
    shop.slackAlert("/api/plan/cancel : No active subscription found for shop : " + req.body.shop.myshopify_domain)
    res.json({error : "No active subscription found", done : false})
    return
  }

  try {

    let gqlRequest = {
      "query": `mutation AppSubscriptionCancel($id: ID!) {
        appSubscriptionCancel(id: $id) {
          userErrors {
            field
            message
          }
          appSubscription {
            id
            status
          }
        }
      }`,
      "variables": {
        "id": subs.subscription_id
      },
    }

    let responseJson = {}
    var Token = new TokenService(req.body.shop.shop_id)
    let access_token = await Token.getToken()
    responseJson = await shop.graphQlQuery(
      req.body.shop.myshopify_domain,
      access_token,
      gqlRequest
    )

    let subsData = {
      shop_id : subs.shop_id,
      subscription_id : subs.subscription_id,
      status : subs.status
    }

    let errors = ""
    if (responseJson && responseJson.data && responseJson.data.appSubscriptionCancel) {
        let resp = responseJson.data.appSubscriptionCancel
        errors = resp.userErrors ?? [];
        if (errors.length > 0) {
            logger.error("Error in /api/plan/cancel : ", errors)
            throw new Error("AppSubscriptionCancel userError")
        }
        subsData.subscription_id = resp.appSubscription.id
        subsData.status = resp.appSubscription.status
    } else {
        throw new Error("AppSubscriptionCancel error")
    }

    // Updates status
    let done = await upsertSubscription(subsData)

    res.json({error : "", done : done})
  } catch (err) {
    logger.error("Error in /api/plan/cancel : " + (err.message ?? ""), err)
    shop.slackAlert("Error in /api/plan/cancel : " + (err.message ?? ""))
    res.json({error : "Something went wrong. We're working to resolve the issue.", done: false})
  }
})

app.post('/api/plan/upgrade', authMiddleware, resolveSelectedShop, async (req, res) => {

  if (!req.body.plan) {
    res.json({error : "Plan not specified", redirect : ""})
    return
  }
  
  let validPlans = [...OLD_PRICING_PLANS, ...NEW_PRICING_PLANS];
  if (validPlans.indexOf(req.body.plan) == -1) {
    res.json({error : "Invalid plan specified", redirect : ""})
    return
  }

  let billingDetails = shopifyBillingConfig[req.body.plan] ?? null;
  if (!billingDetails) {
    res.json({error : "Invalid plan specified", redirect : ""})
    return
  }
  let discount = false;
  if (!!req.body.coupon) {

    let coupon = req.body.coupon ?? "";
    coupon = coupon.trim().toUpperCase();
  
    let couponConfig = await evaluateCoupon(coupon, req.body.plan)
    if (!couponConfig || !couponConfig.discount) {
      res.json({error : "Invalid coupon", redirect : ""})
      return
    }

    discount = couponConfig.discount;
  }

  try {

    var Token = new TokenService(req.body.shop.shop_id)
    let access_token = await Token.getToken()
    if (!access_token) {
      res.json({error : "Invalid access token", redirect : ""})
      return
    }

    let currentActiveSubscriptions = await getActiveSubscriptionsFromShopify(
      req.body.shop.myshopify_domain,
      access_token
    );

    if (currentActiveSubscriptions && currentActiveSubscriptions.length > 0) {
      let activePlanName = currentActiveSubscriptions[0].name ?? '';
      if (activePlanName == req.body.plan) {
        shop.slackAlert("Active plan exists for shop : " + req.body.shop.myshopify_domain)
        res.json({redirect : "", error : "Your plan is already active. Please refresh the page."})
        return;
      }
    }

    let billingDetailsLineItem = {
      "price": {
        "amount": billingDetails.amount,
        "currencyCode": billingDetails.currencyCode
      },
      "interval": billingDetails.interval
    }

    if (!!discount) {
      billingDetailsLineItem.discount = discount
    }

    let gqlRequest = {
      "query": `mutation AppSubscriptionCreate($name: String!, $lineItems: [AppSubscriptionLineItemInput!]!, $returnUrl: URL!, $trialDays: Int, $test: Boolean) {
        appSubscriptionCreate(name: $name, returnUrl: $returnUrl, lineItems: $lineItems, trialDays: $trialDays, test: $test) {
          userErrors {
            field
            message
          }
          appSubscription {
            id
            status
          }
          confirmationUrl
        }
      }`,
      "variables": {
        "name": req.body.plan,
        "returnUrl": process.env.HOST + '/api/plan/check',
        "trialDays": billingDetails.trialDays,
        "test": req.body.is_admin ?? false,
        "lineItems": [
          {
            "plan": {
              "appRecurringPricingDetails": billingDetailsLineItem
            }
          }
        ]
      },
    }

    let responseJson = {}
    responseJson = await shop.graphQlQuery(
      req.body.shop.myshopify_domain,
      access_token,
      gqlRequest
    )

    let subscription_record = {
        trial_days : billingDetails.trialDays,
        amount : billingDetails.amount,
        plan_type : billingDetails.planType,
        coupon: req.body.coupon ?? "",
        name : req.body.plan,
        status : "DRAFT",
        shop_id : req.body.shop.shop_id
    }

    let confirmationUrl = ""
    if (responseJson && responseJson.data && responseJson.data.appSubscriptionCreate) {
        let resp = responseJson.data.appSubscriptionCreate
        confirmationUrl = resp.confirmationUrl
        subscription_record.subscription_id = resp.appSubscription.id
        subscription_record.status = resp.appSubscription.status
    }

    var ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress 
    tracker.track(req.body.shop.myshopify_domain, 'Shop Try Subscription', {
      ip: ip
    })

    await saveSubscription(subscription_record)

    res.json({redirect : confirmationUrl, error : ""})
  } catch (err) {
    logger.error("Error in /api/plan/upgrade : " + (err.message ?? ""), err)
    shop.slackAlert("Error in /api/plan/upgrade : " + (err.message ?? ""))
    res.json({error : "Something went wrong. We're working to resolve the issue.", redirect : ""})
  }
})

app.get('/api/token', authMiddleware, resolveSelectedShop, async (req, res) => {

  let integration_request_id = req.query.integration_request_id ?? "";

  let shop_id = req.body.shop.shop_id;
  let payload = {}
  if (!!integration_request_id) {
    // Integration request id is provided, use it to get the fb account id
    let intg = await integrations.getIntegrationRequest(integration_request_id)
      if (!!intg && !!intg.request_id && intg.source_type == SOURCE_FB) {
        let source_config_id = await integrations.parseSourceConfigId(intg)
        if (!!source_config_id) {
          payload = {
            account_id: source_config_id,
            cube: req.query.cube,
          };

          // payload = {
          //   account_id : "*****************",
          //   cube : req.query.cube,
          // }
        }
      }
  } else {
    payload = {
      shop_id,
      cube: req.query.cube,
    };

    let useMigrationDataset = await shop.shouldUseMigrationDataset(req.body.shop.shop_id);
    if (useMigrationDataset) {
      payload.overrideDataset = DBT_PROD_DATASET_OSS
    }
  }

  if (Object.keys(payload).length == 0) {
    res.json({error : "Invalid token request"})
    return
  }

  res.json({
    // Take note: cubejs expects the JWT payload to contain an object!
    token: jwt.sign(payload, process.env.CUBEJS_API_SECRET, { expiresIn: '1d'}),
  });
});


app.post('/api/feedback', authMiddleware, resolveUserShop, async (req, res) => {
  if (!req.body.subject || !req.body.rating) {
    logger.error("Feedback subject/rating not specified")
    res.json({success: false, error : "subject not specified"})
    return
  }

  await shop.recordFeedback(req.body.resolved_login_id, {
    [req.body.subject] : req.body.rating
  })

  res.json({success: true, error: ""})
});

app.post('/api/blacklist', authMiddleware, resolveUserShop, async (req, res) => {
  if (!req.body.type) {
    res.json({success: false, error : "type not specified"})
    return
  }

  await shop.blacklistFor(req.body.type, req.body.resolved_login_id)
  res.json({success: true, error: ""})
});

app.post('/api/login', authMiddleware, resolveUserShop, async (req, res) => {
  const response = await shop.getLoginConfig(req.body);

  /** clear only if user is onboarded and there is no additional shop available to link,
   *  since the cookie will be used for linking the shop */
  if (response.userOnboarded && (!response.linkUserShop || !response.linkUserShop.shop_id)) {
    res.clearCookie(SESSION_COOKIE_NAME); // clear session cookie
    res.clearCookie(`${SESSION_COOKIE_NAME}.sig`); // clear session cookie signature
    res.clearCookie(STATE_COOKIE_NAME);
  }
  res.json(response)
});

app.post('/api/shop/config', authMiddleware, resolveSelectedShop, async (req, res) => {
  const response = await shop.getShopConfig(req.body.shop, req.body.is_admin);
  res.json(response)
})

// Handle the OAuth 2.0 callback
app.get("/api/auth/google/callback", authMiddleware, resolveSelectedShop, async (req, res) => {
  const { code, state } = req.query;

  const result = await googleIntegration.handleOAuthCallback(state, code);

  if (result.error) {
    logger.error(`Error in /api/auth/google/callback: ${result.error}`, { state });
    const fallbackUrl = `${process.env.HOST}`; // Fallback URL in case redirect_url is not provided
    return res.redirect(result.redirect_url || fallbackUrl);
  }

  return res.redirect(result.redirect_url);
});

app.post("/api/integrations/configure/options", authMiddleware, resolveSelectedShop, async (req, res) => {
  const shop_id = req.body.shop.shop_id;
  const { request_id } = req.body;
 
  if (!shop_id || !request_id) {
    res.json({error: errors.client.invalidInput});
    return;
  }

  try {
    const data = await integrations.getConfigureOptions(request_id);
    if (data.error) {
      res.json({error: errors.client.generic});
      return;
    }
    res.json(data);
  } catch (error) {
    logger.error('Error retrieving Account options for integration', {message : error.message ?? "", request_id});
    res.json({error: errors.client.generic})
  }
});

app.post('/api/integrations/configure',authMiddleware, resolveSelectedShop, async (req, res) => {
  const { options, request_id } = req.body;

  if (!options || !request_id || !Array.isArray(options)) {
    logger.error('Invalid input in /api/integrations/configure', {options, request_id});
    return res.json({ error: errors.client.invalidInput });
  }

  try {
    const {error} = await integrations.configureSource(request_id, options);
    if (error) {
      return res.json({ error: errors.client.generic });
    }

    res.json({ success: true });
  } catch (error) {
    logger.error('Error creating source in createOverrideSource:', {message : error.message ?? ""});
    res.json({ error : errors.client.generic })
  }
});

app.get("/api/integrations/authover/:request_id", authMiddleware, resolveSelectedShop, async (req, res) => {

  if (!req.query.secret_id
      || req.query.secret_id == ''
      || !req.params.request_id
      || req.params.request_id == "") {
    return res
      .status(200)
      .set("Content-Type", "text/html")
      .send(`<html><p>Failed to connect. <a href="${process.env.HOST}">Home</a></p></html>`);
  }

  let done = await integrations.updatePartial(req.params.request_id, {
    secret_id : req.query.secret_id,
    status : 1
  })

  var ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress 
  tracker.track(req.body.shop.myshopify_domain, 'Source Connected', {
    ip: ip,
    source_type: SOURCE_FB
  })

  if (!done) {
    return res
      .status(200)
      .set("Content-Type", "text/html")
      .send("<html><p>Failed to connect.</p></html>");
  }

  res.redirect(`/integrations?auth=success&request_id=${req.params.request_id}`);
});


// it act as the returnUrl for shopify app subscription approval screen
// it first checks if there's any active subscription, if yes, redirect to /
// if not - check from shopify - if found on shopify - update active subscription in db
// if not - redirect to / -> user denied the subscription in this case
app.get('/api/plan/check', authMiddleware, resolveSelectedShop, async (req, res) => {

  try {
    const { shop_id, myshopify_domain } = req.body.shop;
    logger.info(`-------> api/plan/check ${shop_id}`)

    // Get active subscription from local database
    let subs = await getActiveSubscription(shop_id);

    // If there's an active subscription, redirect to home
    if (subs && subs.subscription_id) {
      return res.redirect('/book-call?mt=sub');
    }

    // Create a new token service
    const Token = new TokenService(shop_id);

    // Get the access token
    let access_token = await Token.getToken();

    // Get active subscriptions from Shopify
    let currentActiveSubscriptions = await getActiveSubscriptionsFromShopify(myshopify_domain, access_token);

    // If there's an active subscription on Shopify, update the record in local database
    if (currentActiveSubscriptions && currentActiveSubscriptions.length > 0) {
      let currentSubs = currentActiveSubscriptions[0];
      await updateSubscription(currentSubs.id ?? "", currentSubs.status, shop_id);
    }

    // Redirect to home
    res.redirect('/book-call?mt=sub');
  } catch (err) {
    // Log the error and redirect to home
    logger.error(`/api/plan/check error - ${err}`);
    res.redirect('/?mt=suberr');
  }
});

app.get('/exitiframe', (req, res) => {

  logger.info(`/exiframe query parameters`, {
    query : req.query
  });
  return res.status(200).send('OK').end();
});

app.use(shopify.cspHeaders());
app.use(serveStatic(STATIC_PATH, { index: false }));

// TODO: add rate limit
app.post("/api/identify-user/email-submit", async (req, res, _next) => {
  const { email, shop_id } = req.body

  if (!email || !shop_id) {
    return res.status(400).json({
      status: false,
      message: 'Invalid email or shop_id'
    });
  }

  const response = await Identification.handleEmailSubmit(email, shop_id);
  res.json(response);
});

// TODO: add rate limit
app.post("/api/create-user", async (req, res, _next) => {
  try {
    const { id_token } = req.body;

    if (!id_token) {
      return res.status(401).json({ error: 'No ID token provided' });
    }

    const decodedToken = await admin.auth().verifyIdToken(id_token);

    const email = decodedToken.email;
    const uid = decodedToken.uid;

    if (!email || !uid) {
      logger.error('Invalid token in create-user. No email or uid found in ID token', { email, uid });
      return res.status(400).json({ error: 'The token provided is not valid.' });
    }

    let user_service = new User();
    const response = await user_service.createNewAuthedUser(uid, email);
    res.json(response);
  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({ error: 'Failed to create user' });
  }
});

app.get("/sign-in", async (_req, res, _next) => {
  return res
    .status(200)
    .set("Content-Type", "text/html")
    .send(readFileSync(join(STATIC_PATH, "index.html")));
});

app.get("/sign-up", async (_req, res, _next) => {
  return res
    .status(200)
    .set("Content-Type", "text/html")
    .send(readFileSync(join(STATIC_PATH, "index.html")));
});

app.use("/*", authMiddleware, async (_req, res, _next) => {
  return res
    .status(200)
    .set("Content-Type", "text/html")
    .send(readFileSync(join(STATIC_PATH, "index.html")));
});

app.listen(PORT);
