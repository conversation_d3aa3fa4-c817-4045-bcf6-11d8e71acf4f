import React, { useState } from "react";
import { toast } from 'react-toastify';
import NiceModal from "@ebay/nice-modal-react";

// @mui material components
import Card from "@mui/material/Card";
import Icon from "@mui/material/Icon";
import CircularProgress from "@mui/material/CircularProgress";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDInput from "@/components/MDInput";
import MDButton from "@/components/MDButton";

// Authentication layout components
import BasicLayout from "@/layouts/authentication/components/BasicLayout";
import bgImage from "@/assets/images/bg-sign-in-basic.jpeg";

import { useCancellableAxios, useMaterialUIController } from "@/context";
import { InviteMemberDialog } from "@/layouts/pages/account/settings/components/Members/invite-member";

// Shop flow identification component (similar to identification modal)
function ShopFlowInvite() {
  const [controller] = useMaterialUIController();
  const { loginConfig } = controller;
  const axiosInstance = useCancellableAxios();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [emailSubmitStart, setEmailSubmitStart] = useState(false);
  const [flowType, setFlowType] = useState(null);
  const [passwordSubmitStart, setPasswordSubmitStart] = useState(false);
  const [subHeadingText, setSubHeadingText] = useState("Please enter email to identify");

  const loginShop = loginConfig?.shop;

  const handleEmailSubmit = () => {
    if (emailSubmitStart) {
      return;
    }

    setEmailSubmitStart(true);
    axiosInstance.post("/api/identify-user/email-submit", {
      email,
      shop_id: loginConfig?.shop?.shop_id
    })
      .then((response) => {
        setEmailSubmitStart(false);
        if (response.data && response.data.status) {
          setFlowType(response.data.flow);
          setSubHeadingText(response.data.flow.includes("register") ? 
            "Create a password to complete registration" : 
            "Enter your password to continue");
        } else {
          toast.error('Something went wrong. Please try again.');
        }
      })
      .catch((error) => {
        setEmailSubmitStart(false);
        toast.error(error.response?.data?.message || 'Something went wrong. Please try again.');
      });
  };

  const handlePasswordSubmit = () => {
    if (passwordSubmitStart) {
      return;
    }

    if (flowType.includes("register") && password !== confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    setPasswordSubmitStart(true);
    axiosInstance.post("/api/identify-user/password-submit", {
      email,
      password,
      shop_id: loginConfig?.shop?.shop_id,
      flow: flowType
    })
      .then((response) => {
        setPasswordSubmitStart(false);
        if (response.data && response.data.status) {
          toast.success(flowType.includes("register") ? 
            "Account created successfully! You now have access to this store." :
            "Successfully linked to your account!");
          // Refresh the page to update the login state
          window.location.reload();
        } else {
          toast.error(response.data?.message || 'Something went wrong. Please try again.');
        }
      })
      .catch((error) => {
        setPasswordSubmitStart(false);
        toast.error(error.response?.data?.message || 'Something went wrong. Please try again.');
      });
  };

  const handleEditEmail = () => {
    setFlowType(null);
    setSubHeadingText("Please enter email to identify");
  };

  return (
    <BasicLayout image={bgImage} chooseLanguage={false}>
      <MDBox py={4}></MDBox>
      <MDBox py={1.3}></MDBox>
      <Card>
        <MDBox pt={4} pb={1} px={3}>
          <MDBox display="flex" flexDirection="column" justifyContent="center" alignItems="center" mb={2}>
            <MDTypography variant="button" color="secondary" textAlign="center" fontWeight="medium">
              Invite Team Member to {loginShop?.name}
            </MDTypography>
            <MDTypography variant="button" color="secondary" textAlign="center" fontWeight="regular">
              {subHeadingText}
            </MDTypography>
          </MDBox>
          <MDBox component="form" role="form">
            {!flowType && (
              <MDBox mb={2}>
                <MDInput
                  type="email"
                  label="Email"
                  onChange={(e) => { setEmail(e.target.value) }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && email !== "") {
                      e.preventDefault();
                      handleEmailSubmit();
                    }
                  }}
                  value={email}
                  variant="standard"
                  fullWidth
                  placeholder="<EMAIL>"
                  InputLabelProps={{ shrink: true }}
                />
                <MDBox mt={3} mb={1}>
                  <MDButton
                    variant="gradient"
                    color={(email === "") ? "light" : "info"}
                    fullWidth
                    fontWeight="bold"
                    onClick={handleEmailSubmit}
                    disabled={email === ""}
                    sx={{
                      "&:disabled": {
                        color: "grey"
                      }
                    }}
                  >
                    {emailSubmitStart ? <CircularProgress size={20} color="white" /> : "Continue"}
                  </MDButton>
                </MDBox>
              </MDBox>
            )}
            {email && flowType && (flowType.includes("register") || flowType.includes("login")) && (
              <MDBox mb={2}>
                <MDTypography variant="body2" color="secondary"
                  sx={{
                    fontSize: "0.75rem",
                    fontWeight: 400,
                    lineHeight: 1.25,
                    letterSpacing: "0.00938em",
                    marginBottom: "1em",
                  }}
                >
                  Email: {email}&nbsp;
                  <Icon
                    sx={{
                      fontSize: "0.75rem",
                      cursor: "pointer",
                      verticalAlign: "top",
                      "&:hover": {
                        opacity: 0.7
                      }
                    }}
                    onClick={handleEditEmail}
                  >
                    edit_outlined
                  </Icon>
                </MDTypography>
                <MDBox mb={2}>
                  <MDInput
                    type="password"
                    label={flowType.includes("register") ? "New Password" : "Password"}
                    onChange={(e) => { setPassword(e.target.value) }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && password !== "" && (confirmPassword !== "" || !flowType.includes("register"))) {
                        e.preventDefault();
                        handlePasswordSubmit();
                      }
                    }}
                    value={password}
                    variant="standard"
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                  />
                </MDBox>
                {flowType.includes("register") && (
                  <MDBox mb={2}>
                    <MDInput
                      type="password"
                      label="Confirm Password"
                      onChange={(e) => { setConfirmPassword(e.target.value) }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && password !== "" && confirmPassword !== "") {
                          e.preventDefault();
                          handlePasswordSubmit();
                        }
                      }}
                      value={confirmPassword}
                      variant="standard"
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                    />
                  </MDBox>
                )}
                <MDBox mt={3} mb={1}>
                  <MDButton
                    variant="gradient"
                    color={(password === "") ? "light" : "info"}
                    fullWidth
                    fontWeight="bold"
                    onClick={handlePasswordSubmit}
                    disabled={password === "" || (flowType.includes("register") && confirmPassword === "")}
                    sx={{
                      "&:disabled": {
                        color: "grey"
                      }
                    }}
                  >
                    {passwordSubmitStart ? <CircularProgress size={20} color="white" /> : 
                      flowType.includes("register") ? "Register" : "Login"}
                  </MDButton>
                </MDBox>
              </MDBox>
            )}
          </MDBox>
        </MDBox>
      </Card>
    </BasicLayout>
  );
}

// User flow invite component - opens existing workspace invite modal
function UserFlowInvite() {
  const [controller] = useMaterialUIController();
  const { loginConfig, selectedShop } = controller;

  // Get current workspace and shop info
  const activeWorkspace = (loginConfig?.workspaces ?? []).find((w) => w.workspace_id === loginConfig?.selectedWorkspaceId);
  const currentShop = activeWorkspace?.shops?.find((shop) => shop.myshopify_domain === selectedShop);

  const handleInviteMember = () => {
    // Open the existing workspace invite modal with current shop context
    NiceModal.show(InviteMemberDialog, {
      shops: currentShop ? [currentShop] : [],
      workspaceId: activeWorkspace?.workspace_id,
      fetchWorkspaceDetails: () => {
        // Refresh the page or update state as needed
        window.location.reload();
      }
    });
  };

  return (
    <Card>
      <MDBox p={3}>
        <MDBox mb={3}>
          <MDTypography variant="h5" fontWeight="medium">
            Invite Team Member
          </MDTypography>
          <MDTypography variant="body2" color="text" mt={1}>
            Invite a team member to access <strong>{currentShop?.name}</strong> in your workspace.
          </MDTypography>
        </MDBox>

        <MDButton
          variant="gradient"
          color="info"
          onClick={handleInviteMember}
          startIcon={<Icon>person_add</Icon>}
        >
          Invite Team Member
        </MDButton>
      </MDBox>
    </Card>
  );
}

// Main component that decides which flow to show
function InviteMember() {
  const [controller] = useMaterialUIController();
  const { loginConfig } = controller;

  const isShopFlow = !loginConfig.userData || !loginConfig.userData.email;

  if (isShopFlow) {
    return <ShopFlowInvite />;
  } else {
    return <UserFlowInvite />;
  }
}

export default InviteMember;
