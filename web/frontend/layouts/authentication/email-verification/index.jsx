import { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";

// Material Dashboard 2 PRO React components
import Card from "@mui/material/Card";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";
import { toast } from 'react-toastify';
import { getAuth, signOut } from "firebase/auth";
import bgImage from "@/assets/images/bg-sign-in-basic.jpeg";
import { app } from '@/firebase-config';
import BasicLayout from "@/layouts/authentication/components/BasicLayout";
import { setLoginConfig, useMaterialUIController, useCancellableAxios } from "@/context";
import { useTranslation } from "react-i18next";
import CircularProgress from "@mui/material/CircularProgress";
import axios from "axios";
import { isShopifyIdentificationEnabled } from "@/utils/featuresupport";

function EmailVerification() {
  const [controller, dispatch] = useMaterialUIController();
  const {t} = useTranslation();
  const [sendingEmail, setSendingEmail] = useState(false);
  const [disableButton, setDisableButton] = useState(false);
  const [countdown, setCountdown] = useState(null);

  const navigate = useNavigate();
  const location = useLocation();
  const auth = getAuth(app);
  const axiosInstance = useCancellableAxios();

  useEffect(() => {
    const { loginConfig } = controller;
    const checkVerification = setInterval(() => {
      auth.currentUser.reload().then(() => {
        if (auth.currentUser.emailVerified) {
          setLoginConfig(dispatch, {user : auth.currentUser})
          clearInterval(checkVerification);
        }
      });
    }, 20000); // Check every 20 seconds
  
    const timeoutId = setTimeout(() => {
      clearInterval(checkVerification);
    }, 600000); // Clear interval after 10 minutes
  
    return () => {
      clearInterval(checkVerification);
      clearTimeout(timeoutId);
    };
  }, [auth]);

  const handleResendVerificationEmail = () => {

    if (sendingEmail) {
      return;
    }

    setSendingEmail(true);
    setDisableButton(true);
    setCountdown(30); // 30 seconds  

    axiosInstance.post("/api/user/verify-email", {})
      .then((resp) => {

        if (resp.data && resp.data.alreadyVerified) {
          toast.error(t("email-already-verified"));
          return;
        }

        toast.success(t("verif-email-sent"));
        setSendingEmail(false);
        const timer = setInterval(() => {
          setCountdown((prevCountdown) => {
            if (prevCountdown === 1) {
              clearInterval(timer);
              setDisableButton(false);
              return null;
            } else {
              return prevCountdown - 1;
            }
          });
        }, 1000);
      })
      .catch((error) => {

        if (!axios.isCancel(error)) {
          if (error.response && error.response.data && error.response.data.error) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            toast.error(error.response.data.error);
          } else {
            toast.error(t("something-went-wrong"));
          }
        }
        setSendingEmail(false);
        setDisableButton(false);
        setCountdown(null);
        console.error(error);
      });
  };

  let handleSignOut = () => {
    signOut(auth).then(() => {
        navigate(`/sign-in`, {replace:true})
    }).catch((error) => {
      console.error(error)
    });
  }

  useEffect(() => {
    handleResendVerificationEmail();
  }, []);

  return (
    <BasicLayout image={bgImage}>
    <MDBox height="100vh" display="flex" flexDirection="column" justifyContent="center" alignItems="center">
      <Card>
      <MDBox display="flex" flexDirection="column" justifyContent="center" alignItems="center" m={4}> 
      <MDTypography variant="h4" component="h1" gutterBottom>
        {t("verify-your-email")}
      </MDTypography>
      <MDTypography variant="button">
        {t("check-verify")}
      </MDTypography>
      <MDBox mt={3} mb={1} width="100%">
        <MDButton variant="outlined" color="primary" fullWidth disabled={disableButton} onClick={handleResendVerificationEmail}>
          {sendingEmail ? <CircularProgress size={20} color="primary" /> : countdown ? `${t("resend-verif-email")} (${countdown})` : t("resend-verif-email")}
        </MDButton>
      </MDBox>
      <MDTypography variant="button" onClick={handleSignOut} color="secondary" mt={1} fontWeight="regular" sx={{cursor: "pointer"}}>
        {t("sign-in-diff-acc")}
      </MDTypography>
      </MDBox>
      </Card>
    </MDBox>
    
    </BasicLayout>
  );
}

export default EmailVerification;